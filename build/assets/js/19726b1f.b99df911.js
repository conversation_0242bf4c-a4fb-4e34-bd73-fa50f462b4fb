"use strict";(self.webpackChunkfunblocks_docs=self.webpackChunkfunblocks_docs||[]).push([[39219],{18044:(e,n,i)=>{i.d(n,{A:()=>v});i(96540);var s=i(50539);const t="socialProofSection_iQYk",o="sectionTitle_K_Fi",a="metricsContainer_WuZH",r="metricCard_3RKd",l="metricIcon_NIST",c="metricValue_qazu",d="metricLabel_Vwzg",h="companiesContainer_Rn5K",m="companiesTitle_lfNe",p="companyGrid_WPY0",x="productHuntContainer_aglb",g="badgesTitle_pxwV",u="badgesWrapper_OcIX",w="badgeLink_snWB",f="badgeImage_TCK6",j="companyItem_kt31";var b=i(9303),A=i(74848);const v=function(e){let{page:n="homepage",customCompanies:i=null,customMetrics:v=null,titleTranslateId:k=null,companiesTitleTranslateId:_=null,showProductHuntBadges:I=!0}=e;const y=[{value:"100,000+",label:(0,A.jsx)(s.A,{id:`${n}.socialProof.users`,children:"Active Users"}),icon:"\ud83d\udc65"},{value:"60%",label:(0,A.jsx)(s.A,{id:`${n}.socialProof.productivity`,children:"Productivity Increase"}),icon:"\ud83d\udcc8"},{value:"80+",label:(0,A.jsx)(s.A,{id:`${n}.socialProof.countries`,children:"Countries"}),icon:"\ud83c\udf0e"},{value:"4.8/5",label:(0,A.jsx)(s.A,{id:`${n}.socialProof.rating`,children:"User Rating"}),icon:"\u2b50"}],N=i||["Google","Amazon","Microsoft","ByteDance","Tencent","XiaoMi","MIT","IBM","Meta","Harvard University","Stanford University","Yale University"],C=v||y;return(0,A.jsx)("section",{id:"social-proof",className:t,children:(0,A.jsxs)("div",{className:"container",children:[(0,A.jsx)(b.A,{as:"h2",className:o,children:(0,A.jsx)(s.A,{id:k||`${n}.socialProof.title`,children:"Trusted by Students and Professionals Worldwide"})}),I&&(0,A.jsxs)("div",{className:x,children:[(0,A.jsx)("p",{className:g,children:(0,A.jsx)(s.A,{id:`${n}.socialProof.productHunt`,children:"Featured on Product Hunt"})}),(0,A.jsxs)("div",{className:u,children:[(0,A.jsx)("a",{href:"https://www.producthunt.com/posts/funblocks-aiflow?embed=true&utm_source=badge-top-post-badge&utm_medium=badge&utm_souce=badge-funblocks-aiflow",target:"_blank",className:w,children:(0,A.jsx)("img",{src:"https://api.producthunt.com/widgets/embed-image/v1/top-post-badge.svg?post_id=486382&theme=dark&period=daily&t=1746785584195",alt:"FunBlocks AIFlow - An AI-powered whiteboard and mind map tool | Product Hunt",className:f})}),(0,A.jsx)("a",{href:"https://www.producthunt.com/posts/funblocks-aiflow?embed=true&utm_source=badge-top-post-topic-badge&utm_medium=badge&utm_souce=badge-funblocks-aiflow",target:"_blank",className:w,children:(0,A.jsx)("img",{src:"https://api.producthunt.com/widgets/embed-image/v1/top-post-topic-badge.svg?post_id=486382&theme=dark&period=weekly&topic_id=204&t=1746785584195",alt:"FunBlocks AIFlow - An AI-powered whiteboard and mind map tool | Product Hunt",className:f})})]})]}),(0,A.jsx)("div",{className:a,children:C.map(((e,n)=>(0,A.jsxs)("div",{className:r,children:[(0,A.jsx)("div",{className:l,children:e.icon}),(0,A.jsx)("div",{className:c,children:e.value}),(0,A.jsx)("div",{className:d,children:e.label})]},n)))}),(0,A.jsxs)("div",{className:h,children:[(0,A.jsx)("p",{className:m,children:(0,A.jsx)(s.A,{id:_||`${n}.socialProof.companies`,children:"Used by innovative teams at"})}),(0,A.jsx)("div",{className:p,children:N.map(((e,n)=>(0,A.jsx)("div",{className:j,children:e},n)))})]})]})})}},26167:(e,n,i)=>{i.d(n,{A:()=>c});const s="footer_m3PR",t="footerContainer_g8s3",o="footerLinks_EjWI",a="toolsGrid_N_gp",r="copyright_zlJy";var l=i(74848);const c=function(){return(0,l.jsx)("footer",{className:s,children:(0,l.jsxs)("div",{className:"container",children:[(0,l.jsxs)("div",{className:t,children:[(0,l.jsxs)("div",{className:o,style:{marginRight:"20px"},children:[(0,l.jsx)("span",{className:"footer-logo",children:"FunBlocks"}),(0,l.jsx)("p",{"data-i18n":"footer.description",style:{color:"#bbb"},children:"An AI-powered platform for visualization-enhanced thinking and productivity."})]}),(0,l.jsxs)("div",{className:o,children:[(0,l.jsx)("h4",{"data-i18n":"footer.product",children:"FunBlocks AI Products"}),(0,l.jsxs)("ul",{children:[(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/aiflow",children:"FunBlocks AI Flow"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/aitools",children:"FunBlocks AI Tools"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/slides",children:"FunBlocks AI Slides"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/aidocs",children:"FunBlocks AI Docs"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/welcome_extension",children:"Chrome Extension: FunBlocks AI Assistant"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/ai_mindmap",children:"Chrome Extension: AI Mindmap Generator"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/prompt_optimizer",children:"Chrome Extension: AI Prompt Optimizer"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/education",children:"FunBlocks AI for Education"})})]})]}),(0,l.jsxs)("div",{className:o,children:[(0,l.jsx)("h4",{"data-i18n":"footer.resources",children:"Resources"}),(0,l.jsxs)("ul",{children:[(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/docs",children:"FunBlocks AI Tutorials"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/blog",children:"FunBlocks AI Blog"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://app.funblocks.net/shares",children:"FunBlocks AI Generated Content"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/collections/Reading",children:"Classic Book Mindmaps"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/collections/Movie",children:"Classic Movie Mindmaps"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/thinking-matters/behind-aiflow",children:"Thinking Matters"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/thinking-matters/category/classic-mental-models",children:"Mental Models"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://www.funblocks.net/ai101",children:"AI Basics: AI 101"})})]})]}),(0,l.jsxs)("div",{className:o,children:[(0,l.jsx)("h4",{"data-i18n":"footer.company",children:"Company"}),(0,l.jsx)("ul",{children:(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://discord.gg/XtdZFBy4uR",target:"_blank",children:"Contact Us"})})})]})]}),(0,l.jsx)("div",{className:t,children:(0,l.jsxs)("div",{className:o,children:[(0,l.jsx)("h4",{"data-i18n":"footer.resources",children:"FunBlocks AI Tools"}),(0,l.jsxs)("div",{className:a,children:[(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/mindmap",target:"_blank",children:"AI Mindmap"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/slides",target:"_blank",children:"AI Slides"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/graphics",target:"_blank",children:"AI Graphics"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/brainstorming",target:"_blank",children:"AI Brainstorming"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/mindkit",target:"_blank",children:"AI MindKit"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/youtube",target:"_blank",children:"AI Youtube Summarizer"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/critical-thinking",target:"_blank",children:"AI Critical Analysis"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/refine-question",target:"_blank",children:"AI Question Craft"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/bias",target:"_blank",children:"AI LogicLens"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/reflection",target:"_blank",children:"AI Reflection"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/decision",target:"_blank",children:"AI Decision Analyzer"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/okr",target:"_blank",children:"AI OKR Assistant"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/startupmentor",target:"_blank",children:"AI Startup Mentor"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/businessmodel",target:"_blank",children:"AI Business Model Analyzer"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/planner",target:"_blank",children:"AI Task Planner"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/counselor",target:"_blank",children:"AI Counselor"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/dreamlens",target:"_blank",children:"AI DreamLens"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/horoscope",target:"_blank",children:"AI Horoscope"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/art",target:"_blank",children:"AI Art Insight"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/photo",target:"_blank",children:"AI Photo Coach"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/poetic",target:"_blank",children:"AI Poetic Lens"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/avatar",target:"_blank",children:"AI Avatar Studio"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/erase",target:"_blank",children:"AI Watermarks Remover"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/reading",target:"_blank",children:"AI Reading Map"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/movie",target:"_blank",children:"AI CineMap"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/feynman",target:"_blank",children:"AI Feynman"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/marzano",target:"_blank",children:"AI Marzano Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/bloom",target:"_blank",children:"AI Bloom Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/solo",target:"_blank",children:"AI SOLO Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/dok",target:"_blank",children:"AI DOK Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/layered-explanation",target:"_blank",children:"AI MindLadder"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/infographic",target:"_blank",children:"AI Infographic"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/insightcards",target:"_blank",children:"AI InsightCards"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/mindsnap",target:"_blank",children:"AI MindSnap"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/one-page-slide",target:"_blank",children:"AI SlideGenius"})]})]})}),(0,l.jsx)("div",{className:r,children:(0,l.jsx)("p",{"data-i18n":"footer.copyright",children:"\xa9 2025 FunBlocks AI. All rights reserved."})})]})})}},34117:(e,n,i)=>{i.r(n),i.d(n,{default:()=>v});var s=i(96540),t=i(34164),o=i(56289),a=i(30300),r=i(50539),l=i(9303);const c={hero:"hero_OKpL",heroRow:"heroRow_KTig",heroContent:"heroContent_yVBG",heroBadge:"heroBadge_OuRS",heroSubtitle:"heroSubtitle_ue8q",heroButtons:"heroButtons_FZNZ",btnPrimary:"btnPrimary_LBf7",btnSecondary:"btnSecondary__ggX",heroStats:"heroStats_drhh",heroStat:"heroStat_SO4g",heroStatNumber:"heroStatNumber_BMfy",heroStatLabel:"heroStatLabel_V_Qw",heroImageContainer:"heroImageContainer_BOdu",heroImageWrapper:"heroImageWrapper_bts7",heroImage:"heroImage_EVpb",benefitsSection:"benefitsSection_PVfJ",sectionHeading:"sectionHeading_MM7S",sectionTitle:"sectionTitle_kXDL",sectionDescription:"sectionDescription__MVy",benefitsGrid:"benefitsGrid_ydtK",benefitCard:"benefitCard_Ox5e",benefitIcon:"benefitIcon_DXbW",benefitTitle:"benefitTitle_QG8t",benefitDescription:"benefitDescription_HrCM",overviewSection:"overviewSection_Lpdo",extensionsGrid:"extensionsGrid_tMDD",extensionCard:"extensionCard_GZcF",extensionImageWrapper:"extensionImageWrapper_Ow3j",extensionImage:"extensionImage_rjDK",extensionBadge:"extensionBadge_BE8D",extensionContent:"extensionContent_uH8R",extensionTitle:"extensionTitle_cGCq",extensionIcon:"extensionIcon_rcAK",extensionSubtitle:"extensionSubtitle_nruu",extensionDescription:"extensionDescription_i1P4",extensionFeatures:"extensionFeatures_xmzJ",extensionFeature:"extensionFeature_FgiD",featureIcon:"featureIcon_jEtE",extensionButtons:"extensionButtons_IHWe",extensionButtonPrimary:"extensionButtonPrimary_G7hZ",extensionButtonSecondary:"extensionButtonSecondary_yFj4",comparisonSection:"comparisonSection_hWrl",comparisonTable:"comparisonTable_mmhc",comparisonHeader:"comparisonHeader_M8s2",comparisonRow:"comparisonRow_WThv",comparisonFeature:"comparisonFeature_AJG_",comparisonCell:"comparisonCell_dcof",comparisonNote:"comparisonNote_VDxM"};var d=i(26167),h=i(87263),m=i(79912),p=i(81896),x=i(78905),g=i(38876),u=i(18044),w=i(74848);function f(e){let{setShowImageSrc:n}=e;return(0,w.jsx)("section",{id:"hero",className:(0,t.A)(c.hero,c.pageSection),style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)"},children:(0,w.jsx)("div",{className:"container",children:(0,w.jsxs)("div",{className:c.heroRow,children:[(0,w.jsxs)("div",{className:c.heroContent,style:{flex:1,minWidth:0},children:[(0,w.jsx)("div",{className:c.heroBadge,children:(0,w.jsx)(r.A,{id:"ai_extensions.hero.badge",children:"BROWSER EXTENSIONS"})}),(0,w.jsx)(l.A,{as:"h1",children:(0,w.jsx)(r.A,{id:"ai_extensions.hero.title",children:"FunBlocks AI Browser Extensions"})}),(0,w.jsx)("p",{className:c.heroSubtitle,children:(0,w.jsx)(r.A,{id:"ai_extensions.hero.subtitle",children:"Enhance your browsing experience with AI-powered reading, writing, and thinking tools. Available anytime, anywhere on the web."})}),(0,w.jsxs)("div",{className:c.heroButtons,children:[(0,w.jsx)(o.A,{className:(0,t.A)("button",c.btnPrimary),to:"#extensions-overview",children:(0,w.jsx)(r.A,{id:"ai_extensions.hero.explore",children:"Explore Extensions"})}),(0,w.jsx)(o.A,{className:(0,t.A)("button",c.btnSecondary),to:"/",children:(0,w.jsx)(r.A,{id:"ai_extensions.hero.learn_more",children:"Learn More"})})]}),(0,w.jsxs)("div",{className:c.heroStats,children:[(0,w.jsxs)("div",{className:c.heroStat,children:[(0,w.jsx)("span",{className:c.heroStatNumber,children:"3"}),(0,w.jsx)("span",{className:c.heroStatLabel,children:(0,w.jsx)(r.A,{id:"ai_extensions.hero.stat1",children:"Powerful Extensions"})})]}),(0,w.jsxs)("div",{className:c.heroStat,children:[(0,w.jsx)("span",{className:c.heroStatNumber,children:"100K+"}),(0,w.jsx)("span",{className:c.heroStatLabel,children:(0,w.jsx)(r.A,{id:"ai_extensions.hero.stat2",children:"Active Users"})})]}),(0,w.jsxs)("div",{className:c.heroStat,children:[(0,w.jsx)("span",{className:c.heroStatNumber,children:"4.8\u2605"}),(0,w.jsx)("span",{className:c.heroStatLabel,children:(0,w.jsx)(r.A,{id:"ai_extensions.hero.stat3",children:"User Rating"})})]})]})]}),(0,w.jsx)("div",{className:c.heroImageContainer,style:{flex:1,minWidth:0,display:"flex",justifyContent:"center"},children:(0,w.jsx)("div",{className:c.heroImageWrapper,children:(0,w.jsx)("img",{className:c.heroImage,onClick:()=>n("/img/portfolio/fullsize/all_in_one_en.png"),id:"ai-extensions-overview",alt:"FunBlocks AI Browser Extensions overview",src:"/img/portfolio/fullsize/all_in_one_en.png"})})})]})})})}function j(){const e=[{icon:"\u26a1",title:(0,r.T)({id:"ai_extensions.benefits.convenience.title",message:"Always Available Convenience"}),description:(0,r.T)({id:"ai_extensions.benefits.convenience.description",message:"Access powerful AI tools instantly while browsing any website or working on any task, without switching between applications."})},{icon:"\ud83d\udd04",title:(0,r.T)({id:"ai_extensions.benefits.context.title",message:"Automatic Context Capture"}),description:(0,r.T)({id:"ai_extensions.benefits.context.description",message:"Automatically capture webpage content as context for AI assistance, eliminating the need for manual copy-pasting."})},{icon:"\ud83d\ude80",title:(0,r.T)({id:"ai_extensions.benefits.efficiency.title",message:"Dramatically Improved Efficiency"}),description:(0,r.T)({id:"ai_extensions.benefits.efficiency.description",message:"Process web content with AI assistance to boost productivity by 10x in reading, writing, and thinking tasks."})},{icon:"\ud83e\udde0",title:(0,r.T)({id:"ai_extensions.benefits.thinking.title",message:"Enhanced Critical Thinking"}),description:(0,r.T)({id:"ai_extensions.benefits.thinking.description",message:"Develop stronger analytical skills with AI-powered critical thinking frameworks and structured analysis tools."})}];return(0,w.jsx)("section",{className:c.benefitsSection,children:(0,w.jsxs)("div",{className:"container",children:[(0,w.jsxs)("div",{className:c.sectionHeading,children:[(0,w.jsx)(l.A,{as:"h2",className:c.sectionTitle,children:(0,w.jsx)(r.A,{id:"ai_extensions.benefits.title",children:"Key Benefits"})}),(0,w.jsx)("p",{className:c.sectionDescription,children:(0,w.jsx)(r.A,{id:"ai_extensions.benefits.description",children:"Discover how FunBlocks AI Browser Extensions transform your daily web experience with powerful AI capabilities."})})]}),(0,w.jsx)("div",{className:c.benefitsGrid,children:e.map(((e,n)=>(0,w.jsxs)("div",{className:c.benefitCard,children:[(0,w.jsx)("div",{className:c.benefitIcon,children:e.icon}),(0,w.jsx)(l.A,{as:"h3",className:c.benefitTitle,children:e.title}),(0,w.jsx)("p",{className:c.benefitDescription,children:e.description})]},n)))})]})})}function b(e){let{setShowImageSrc:n}=e;const i=[{id:"funblocks-ai-extension",badge:"COMPREHENSIVE",icon:"/img/icon.png",title:(0,r.T)({id:"ai_extensions.overview.extension1.title",message:"FunBlocks AI Assistant"}),subtitle:(0,r.T)({id:"ai_extensions.overview.extension1.subtitle",message:"Complete AI-powered browsing assistant with writing, reading, and thinking tools"}),description:(0,r.T)({id:"ai_extensions.overview.extension1.description",message:"The most comprehensive AI browser extension that includes all features from our other extensions plus advanced writing assistance, contextual AI tools, and seamless integration with FunBlocks AIFlow."}),features:[(0,r.T)({id:"ai_extensions.overview.extension1.feature1",message:"AI Writing Assistant with contextual toolbar"}),(0,r.T)({id:"ai_extensions.overview.extension1.feature2",message:"AI Reading Assistant with critical thinking frameworks"}),(0,r.T)({id:"ai_extensions.overview.extension1.feature3",message:"Mind mapping and brainstorming tools"}),(0,r.T)({id:"ai_extensions.overview.extension1.feature4",message:"Smart widgets for email, video, and more"})],link:"/funblocks-ai-assistant",chromeStoreLink:"https://chromewebstore.google.com/detail/funblocks-ai-assistant-%E2%80%93/coodnehmocjfaandkbeknihiagfccoid",image:"/img/portfolio/fullsize/ai_reading_en.png",badgeColor:"#4CAF50"},{id:"ai-mindmap-generator",badge:"FOCUSED",icon:"/img/mindmap-icon.png",title:(0,r.T)({id:"ai_extensions.overview.extension2.title",message:"AI MindMap Generator"}),subtitle:(0,r.T)({id:"ai_extensions.overview.extension2.subtitle",message:"Transform web content and videos into visual mind maps instantly"}),description:(0,r.T)({id:"ai_extensions.overview.extension2.description",message:"Specialized extension focused on helping users read and understand web content and YouTube videos by generating visual mind maps, brainstorming, and critical analysis."}),features:[(0,r.T)({id:"ai_extensions.overview.extension2.feature1",message:"One-click web page mind mapping"}),(0,r.T)({id:"ai_extensions.overview.extension2.feature2",message:"YouTube video transcript analysis"}),(0,r.T)({id:"ai_extensions.overview.extension2.feature3",message:"AI-powered brainstorming and analysis"}),(0,r.T)({id:"ai_extensions.overview.extension2.feature4",message:"Direct integration with FunBlocks AIFlow"})],link:"/ai-mindmap",chromeStoreLink:"https://chromewebstore.google.com/detail/ai-prompt-optimizer-refin/kkcpamahgbfihneanpjomblnbnnfnnjh",image:"/img/portfolio/fullsize/ai_mindmap_sidebar.png",badgeColor:"#2196F3"},{id:"ai-prompt-optimizer",badge:"SPECIALIZED",icon:"/img/prompt-optimizer-icon.png",title:(0,r.T)({id:"ai_extensions.overview.extension3.title",message:"AI Prompt Optimizer"}),subtitle:(0,r.T)({id:"ai_extensions.overview.extension3.subtitle",message:"Enhance AI conversations with prompt optimization and critical thinking"}),description:(0,r.T)({id:"ai_extensions.overview.extension3.description",message:"Specialized tool for improving AI interactions across ChatGPT, Claude, Gemini, and other AI platforms with prompt optimization, critical analysis, and thinking enhancement features."}),features:[(0,r.T)({id:"ai_extensions.overview.extension3.feature1",message:"One-click prompt optimization"}),(0,r.T)({id:"ai_extensions.overview.extension3.feature2",message:"Critical thinking assistant"}),(0,r.T)({id:"ai_extensions.overview.extension3.feature3",message:"Related questions and topics generation"}),(0,r.T)({id:"ai_extensions.overview.extension3.feature4",message:"Multi-platform AI support"})],link:"/prompt-optimizer",chromeStoreLink:"https://chromewebstore.google.com/detail/ai-mindmap-mind-mapping-g/nlalnbdblcdgnammbelmmngehcloildo",image:"/img/portfolio/fullsize/prompt_optimizer_hero.png",badgeColor:"#FF9800"}];return(0,w.jsx)("section",{id:"extensions-overview",className:c.overviewSection,children:(0,w.jsxs)("div",{className:"container",children:[(0,w.jsxs)("div",{className:c.sectionHeading,children:[(0,w.jsx)(l.A,{as:"h2",className:c.sectionTitle,children:(0,w.jsx)(r.A,{id:"ai_extensions.overview.title",children:"Our Browser Extensions"})}),(0,w.jsx)("p",{className:c.sectionDescription,children:(0,w.jsx)(r.A,{id:"ai_extensions.overview.description",children:"Choose the perfect AI extension for your needs. Each extension is designed for specific use cases while maintaining seamless integration with the FunBlocks AI ecosystem."})})]}),(0,w.jsx)("div",{className:c.extensionsGrid,children:i.map((e=>(0,w.jsxs)("div",{className:c.extensionCard,children:[(0,w.jsxs)("div",{className:c.extensionImageWrapper,children:[(0,w.jsx)("img",{className:c.extensionImage,onClick:()=>n(e.image),alt:e.title,src:e.image}),(0,w.jsx)("div",{className:c.extensionBadge,style:{backgroundColor:e.badgeColor},children:e.badge})]}),(0,w.jsxs)("div",{className:c.extensionContent,children:[(0,w.jsxs)(l.A,{as:"h3",className:c.extensionTitle,children:[(0,w.jsx)("div",{style:{width:32,height:32},children:(0,w.jsx)("img",{src:e.icon})}),e.title]}),(0,w.jsx)("p",{className:c.extensionSubtitle,children:e.subtitle}),(0,w.jsx)("p",{className:c.extensionDescription,children:e.description}),(0,w.jsx)("ul",{className:c.extensionFeatures,children:e.features.map(((e,n)=>(0,w.jsxs)("li",{className:c.extensionFeature,children:[(0,w.jsx)("span",{className:c.featureIcon,children:"\u2713"}),e]},n)))}),(0,w.jsxs)("div",{className:c.extensionButtons,children:[(0,w.jsx)(o.A,{className:(0,t.A)("button",c.extensionButtonSecondary),to:e.link,children:(0,w.jsx)(r.A,{id:"ai_extensions.overview.learn_more",children:"Learn More"})}),(0,w.jsx)(o.A,{className:(0,t.A)("button",c.extensionButtonPrimary),href:e.chromeStoreLink,target:"_blank",rel:"noopener noreferrer",children:(0,w.jsx)(r.A,{id:"ai_extensions.overview.install",children:"Install"})})]})]})]},e.id)))})]})})}function A(){return(0,w.jsx)("section",{className:c.comparisonSection,children:(0,w.jsxs)("div",{className:"container",children:[(0,w.jsxs)("div",{className:c.sectionHeading,children:[(0,w.jsx)(l.A,{as:"h2",className:c.sectionTitle,children:(0,w.jsx)(r.A,{id:"ai_extensions.comparison.title",children:"Extension Comparison"})}),(0,w.jsx)("p",{className:c.sectionDescription,children:(0,w.jsx)(r.A,{id:"ai_extensions.comparison.description",children:"Understand the differences between our extensions to choose the right one for your needs."})})]}),(0,w.jsxs)("div",{className:c.comparisonTable,children:[(0,w.jsxs)("div",{className:c.comparisonHeader,children:[(0,w.jsx)("div",{className:c.comparisonFeature,children:(0,w.jsx)(r.A,{id:"ai_extensions.comparison.feature",children:"Feature"})}),(0,w.jsx)("div",{className:c.comparisonExtension,children:(0,w.jsx)(r.A,{id:"ai_extensions.comparison.funblocks_ai",children:"FunBlocks AI Assistant"})}),(0,w.jsx)("div",{className:c.comparisonExtension,children:(0,w.jsx)(r.A,{id:"ai_extensions.comparison.mindmap",children:"AI MindMap Generator"})}),(0,w.jsx)("div",{className:c.comparisonExtension,children:(0,w.jsx)(r.A,{id:"ai_extensions.comparison.prompt_optimizer",children:"AI Prompt Optimizer"})})]}),(0,w.jsxs)("div",{className:c.comparisonRow,children:[(0,w.jsx)("div",{className:c.comparisonFeature,children:(0,w.jsx)(r.A,{id:"ai_extensions.comparison.writing_assistant",children:"AI Writing Assistant"})}),(0,w.jsx)("div",{className:c.comparisonCell,children:"\u2705"}),(0,w.jsx)("div",{className:c.comparisonCell,children:"\u274c"}),(0,w.jsx)("div",{className:c.comparisonCell,children:"\u274c"})]}),(0,w.jsxs)("div",{className:c.comparisonRow,children:[(0,w.jsx)("div",{className:c.comparisonFeature,children:(0,w.jsx)(r.A,{id:"ai_extensions.comparison.mindmapping",children:"Mind Mapping"})}),(0,w.jsx)("div",{className:c.comparisonCell,children:"\u2705"}),(0,w.jsx)("div",{className:c.comparisonCell,children:"\u2705"}),(0,w.jsx)("div",{className:c.comparisonCell,children:"\u274c"})]}),(0,w.jsxs)("div",{className:c.comparisonRow,children:[(0,w.jsx)("div",{className:c.comparisonFeature,children:(0,w.jsx)(r.A,{id:"ai_extensions.comparison.prompt_optimization",children:"Prompt Optimization"})}),(0,w.jsx)("div",{className:c.comparisonCell,children:"\u274c"}),(0,w.jsx)("div",{className:c.comparisonCell,children:"\u274c"}),(0,w.jsx)("div",{className:c.comparisonCell,children:"\u2705"})]}),(0,w.jsxs)("div",{className:c.comparisonRow,children:[(0,w.jsx)("div",{className:c.comparisonFeature,children:(0,w.jsx)(r.A,{id:"ai_extensions.comparison.critical_thinking",children:"Critical Thinking Tools"})}),(0,w.jsx)("div",{className:c.comparisonCell,children:"\u2705"}),(0,w.jsx)("div",{className:c.comparisonCell,children:"\u2705"}),(0,w.jsx)("div",{className:c.comparisonCell,children:"\u2705"})]}),(0,w.jsxs)("div",{className:c.comparisonRow,children:[(0,w.jsx)("div",{className:c.comparisonFeature,children:(0,w.jsx)(r.A,{id:"ai_extensions.comparison.contextual_tools",children:"Contextual AI Tools"})}),(0,w.jsx)("div",{className:c.comparisonCell,children:"\u2705"}),(0,w.jsx)("div",{className:c.comparisonCell,children:"Limited"}),(0,w.jsx)("div",{className:c.comparisonCell,children:"\u274c"})]}),(0,w.jsxs)("div",{className:c.comparisonRow,children:[(0,w.jsx)("div",{className:c.comparisonFeature,children:(0,w.jsx)(r.A,{id:"ai_extensions.comparison.aiflow_integration",children:"AIFlow Integration"})}),(0,w.jsx)("div",{className:c.comparisonCell,children:"\u2705"}),(0,w.jsx)("div",{className:c.comparisonCell,children:"\u2705"}),(0,w.jsx)("div",{className:c.comparisonCell,children:"\u274c"})]})]}),(0,w.jsx)("div",{className:c.comparisonNote,children:(0,w.jsx)("p",{children:(0,w.jsx)(r.A,{id:"ai_extensions.comparison.note",children:"The FunBlocks AI Assistant includes all features from the AI MindMap Generator plus additional writing and contextual tools. Choose the specialized extensions if you only need specific functionality."})})})]})})}function v(){const[e,n]=(0,s.useState)(null);return(0,w.jsxs)(a.A,{title:(0,r.T)({id:"ai_extensions.meta.title",message:"FunBlocks AI Browser Extensions - AI-Powered Web Tools"}),description:(0,r.T)({id:"ai_extensions.meta.description",message:"Enhance your browsing with FunBlocks AI Extensions: AI Assistant, Mindmap Generator, and Prompt Optimizer. Boost productivity with AI-powered reading, writing, and thinking tools."}),children:[(0,w.jsx)(g.A,{}),(0,w.jsx)(p.A,{}),(0,w.jsx)(f,{setShowImageSrc:n}),(0,w.jsx)(j,{}),(0,w.jsx)(b,{setShowImageSrc:n}),(0,w.jsx)(A,{}),(0,w.jsx)(u.A,{}),(0,w.jsx)(h.A,{page:"ai_extensions",faqIds:["q1","q2","q3","q4","q5","q6","q7","q8","q9","q10"]}),(0,w.jsx)(x.A,{toUrl:"#extensions-overview",page:"ai_extensions"}),(0,w.jsx)(d.A,{}),e&&(0,w.jsx)(m.A,{src:e,onClose:()=>n(null)})]})}},38876:(e,n,i)=>{i.d(n,{A:()=>a});i(96540);var s=i(68154),t=i(40797),o=i(74848);const a=function(){const{siteConfig:e}=(0,t.A)(),{url:n}=e;return(0,o.jsxs)(s.m,{children:[(0,o.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"SoftwareApplication",name:"FunBlocks AI Browser Extension",applicationCategory:"BrowserExtension, ProductivityApplication, AIApplication",operatingSystem:"Chrome, Edge",offers:{"@type":"Offer",price:"0",priceCurrency:"USD",availability:"https://schema.org/InStock"},description:"FunBlocks AI Extension: Your smart AI assistant for enhanced reading, writing, and thinking. Features AI-powered brainstorming, mindmapping, critical thinking, and creative writing tools. Compatible with ChatGPT, Claude, Gemini Pro. Boost productivity across all websites.",aggregateRating:{"@type":"AggregateRating",ratingValue:"4.8",ratingCount:"50"},keywords:"AI browser extension, AI writing assistant, AI reading assistant, brainstorming tool, mindmap, critical thinking, creative thinking, productivity extension, Chrome extension, Edge extension, AI mindmapping, AI infographics, AI insight cards"})}),(0,o.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"FAQPage",mainEntity:[{"@type":"Question",name:"What is FunBlocks AI Browser Extension?",acceptedAnswer:{"@type":"Answer",text:"FunBlocks AI Browser Extension is your smart AI assistant for enhanced reading, writing, and thinking across the web. It integrates AI-powered tools for brainstorming, mindmapping, critical thinking, creative writing, and content analysis directly into your browser."}},{"@type":"Question",name:"Which browsers are supported by FunBlocks AI Extension?",acceptedAnswer:{"@type":"Answer",text:"FunBlocks AI Extension currently supports Google Chrome and Microsoft Edge browsers, with more browser support coming soon."}},{"@type":"Question",name:"What AI models does FunBlocks AI Extension support?",acceptedAnswer:{"@type":"Answer",text:"FunBlocks AI Extension supports multiple AI models including OpenAI GPT, Anthropic Claude, and Google Gemini Pro, giving you flexibility to choose the best AI for your specific needs."}},{"@type":"Question",name:"How does FunBlocks AI enhance critical thinking?",acceptedAnswer:{"@type":"Answer",text:"FunBlocks AI enhances critical thinking by providing structured frameworks like Six Thinking Hats, SWOT Analysis, and First Principles Thinking. These frameworks help you analyze content from multiple perspectives, identify strengths and weaknesses, and break down complex topics into fundamental components."}},{"@type":"Question",name:"Can FunBlocks AI help with brainstorming and mindmapping?",acceptedAnswer:{"@type":"Answer",text:"Yes! FunBlocks AI excels at brainstorming and mindmapping. It helps generate ideas, organize thoughts visually, and explore topics from multiple angles simultaneously. The AI assistant can suggest related concepts, help expand on ideas, and create structured visual representations of your thinking process."}}]})}),(0,o.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"HowTo",name:"How to Use FunBlocks AI Browser Extension",description:"Learn how to install and use the FunBlocks AI Browser Extension to enhance your reading, writing, and thinking experience with AI-powered brainstorming, mindmapping, and critical thinking tools.",step:[{"@type":"HowToStep",name:"Install the Extension",text:"Install FunBlocks AI Extension from the Chrome Web Store or Microsoft Edge Add-ons store."},{"@type":"HowToStep",name:"Pin to Browser Toolbar",text:"Click the extension icon in your browser's top-right corner and pin FunBlocks AI to your toolbar for easy access."},{"@type":"HowToStep",name:"Select Text for Analysis",text:"Select any text on a webpage to bring up the FunBlocks AI contextual toolbar with options for translation, explanation, and more."},{"@type":"HowToStep",name:"Use AI Writing Assistant",text:"Click the FunBlocks AI icon in text fields to access the AI writing assistant for content generation and improvement."},{"@type":"HowToStep",name:"Apply Critical Thinking Frameworks",text:"Use built-in frameworks like Six Thinking Hats, SWOT Analysis, and First Principles Thinking to analyze content critically."},{"@type":"HowToStep",name:"Create Visual Content",text:"Generate mindmaps, infographics, and insight cards to visualize complex information and enhance understanding."}],totalTime:"PT5M"})}),(0,o.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"Review",itemReviewed:{"@type":"SoftwareApplication",name:"FunBlocks AI Browser Extension"},author:{"@type":"Person",name:"Sarah K."},reviewRating:{"@type":"Rating",ratingValue:"5",bestRating:"5"},datePublished:"2024-09-15",reviewBody:"The FunBlocks AI Extension has transformed my online workflow completely. The AI writing assistant helps me draft and polish content directly on any website, while the critical thinking tools help me analyze information more deeply. I especially love how the contextual toolbar appears exactly when I need it, offering relevant AI assistance based on what I'm doing. The ability to create visual mind maps from complex articles with one click has been game-changing for content planning. This extension isn't just a tool\u2014it's like having an entire AI workspace that follows me across the web."})})]})}},78905:(e,n,i)=>{i.d(n,{A:()=>m});i(96540);var s=i(34164),t=i(50539);const o="btn_4iM2",a="ctaButtons_Cfhe",r="ctaBtn_Hq_p",l="ctaSection_vQl5";var c=i(9303),d=i(56289),h=i(74848);const m=function(e){let{page:n,toUrl:i,toApp:m,customButtonText:p}=e;return(0,h.jsx)("section",{id:"cta",className:l,children:(0,h.jsxs)("div",{className:"container",children:[(0,h.jsx)(c.A,{as:"h2",children:(0,h.jsx)(t.A,{id:`${n}.cta.title`,children:"Ready to Embark on a Knowledge Adventure?"})}),(0,h.jsx)("p",{children:(0,h.jsx)(t.A,{id:`${n}.cta.subtitle`,children:"Join FunBlocks AIFlow and unleash your limitless cognitive potential!"})}),(0,h.jsx)("div",{className:a,children:(0,h.jsx)(d.A,{className:(0,s.A)(o,r),to:i,onClick:i?void 0:()=>m(),children:p||(0,h.jsx)(t.A,{id:"homepage.cta.button",children:"Start Free Trial"})})})]})})}},79912:(e,n,i)=>{i.d(n,{A:()=>c});i(96540);var s=i(50539);const t="modal_osiT",o="modalImage_HWh8",a="close_Y6T6",r="zoomIndicator_r4Py";var l=i(74848);const c=function(e){let{imageSrc:n,setImageSrc:i}=e;const c=()=>{i(null)};return(0,l.jsxs)("div",{className:t,style:{display:"flex"},onClick:c,children:[(0,l.jsx)("span",{className:a,onClick:c,children:"\xd7"}),(0,l.jsx)("img",{className:o,src:n,alt:(0,s.T)({id:"modal.alt",message:"Enlarged view"})}),(0,l.jsx)("div",{className:r,children:(0,l.jsx)(s.A,{id:"modal.click_to_close",children:"Click to close"})})]})}},81896:(e,n,i)=>{i.d(n,{A:()=>t});i(96540);var s=i(74848);const t=function(e){let{page:n}=e;const i=(["aiflow","homepage"].includes(n)?"flow":"slides"===n&&"slides")||"extension_welcome"===n&&"extension",t=`\n    if (typeof window !== 'undefined') {\n      ${`\n    function handleCredentialResponse(response) {\n      window.open('https://app.funblocks.net/#/login?${i?"source="+i+"&":""}g_login_token=' + response.credential, '_blank');\n    }\n  `}\n\n      // \u52a0\u8f7dGoogle Analytics\u811a\u672c\n      const gaScript = document.createElement('script');\n      gaScript.src = 'https://www.googletagmanager.com/gtag/js?id=G-RYTCZEQK0W';\n      gaScript.async = true;\n      document.head.appendChild(gaScript);\n      \n    window.dataLayer = window.dataLayer || [];\n    function gtag() {\n      window.dataLayer.push(arguments);\n    }\n    gtag('js', new Date());\n    gtag('config', 'G-RYTCZEQK0W');\n  \n\n      // \u52a0\u8f7dGoogle Identity Services\u811a\u672c\n      const gisScript = document.createElement('script');\n      gisScript.src = 'https://accounts.google.com/gsi/client';\n      gisScript.async = true;\n      gisScript.defer = true;\n      document.body.appendChild(gisScript);\n      \n      gisScript.onload = function() {\n        \n    if (typeof window.google !== 'undefined' && window.google.accounts) {\n      window.google.accounts.id.initialize({\n        client_id: '************-enpfsi0n6fo9jqa2aqfr6s37t16loth8.apps.googleusercontent.com',\n        callback: handleCredentialResponse\n      });\n      window.google.accounts.id.prompt();\n    }\n  \n      };\n    }\n  `;return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("script",{dangerouslySetInnerHTML:{__html:t}})})}},87263:(e,n,i)=>{i.d(n,{A:()=>u});var s=i(96540),t=i(34164),o=i(50539);const a="sectionTitle_gwu3",r="faqSection_DBlu",l="faqContainer_pGyA",c="faqItem_sov3",d="faqQuestion_LOEA",h="faqArrow_irh3",m="active_RDQl",p="faqAnswer_HbCX";var x=i(74848);function g(e){let{page:n,questionId:i,answerId:a}=e;const[r,l]=(0,s.useState)(!1);return(0,x.jsxs)("div",{className:(0,t.A)(c,{[m]:r}),children:[(0,x.jsxs)("div",{className:d,onClick:()=>{l(!r)},children:[(0,x.jsx)("span",{style:{fontWeight:"normal"},children:(0,x.jsx)(o.A,{id:`${n}.faq.${i}`})}),(0,x.jsx)("div",{className:h,style:{transform:r?"rotate(90deg)":"none"},children:"\u25b6"})]}),(0,x.jsx)("div",{className:p,style:{whiteSpace:"pre-line",display:r?"block":"none"},children:(0,x.jsx)(o.A,{id:`${n}.faq.${a}`})})]})}const u=function(e){let{page:n,faqIds:i}=e;return(0,x.jsx)("section",{id:"faqs",className:(0,t.A)("page-section",r),style:{backgroundColor:"var(--gray)"},children:(0,x.jsxs)("div",{className:"container",children:[(0,x.jsx)("h2",{className:a,children:(0,x.jsx)(o.A,{id:`${n}.faq.title`,children:"Frequently Asked Questions"})}),(0,x.jsx)("div",{className:l,children:i.map((e=>(0,x.jsx)(g,{page:n,questionId:e,answerId:`a${e.slice(1)}`},e)))})]})})}}}]);