import React from 'react';
import Translate from '@docusaurus/Translate';
import styles from './index.module.css';

import Heading from '@theme/Heading';

/**
 * SocialProofSection component
 *
 * @param {Object} props - Component props
 * @param {string} props.page - Page identifier for translation keys (e.g., 'homepage', 'aiflow')
 * @param {Array} props.customCompanies - Optional custom companies list to override defaults
 * @param {Array} props.customMetrics - Optional custom metrics to override defaults
 * @param {string} props.titleTranslateId - Optional custom title translation ID
 * @param {string} props.companiesTitleTranslateId - Optional custom companies title translation ID
 * @param {boolean} props.showProductHuntBadges - Whether to show Product Hunt badges
 */
function SocialProofSection({
  page = 'homepage',
  customCompanies = null,
  customMetrics = null,
  titleTranslateId = null,
  companiesTitleTranslateId = null,
  showProductHuntBadges = true
}) {
  // Default companies if no custom ones are provided
  const defaultCompanies = [
    'Google',
    'Amazon',
    'Microsoft',
    'ByteDance',
    'Tencent',
    'XiaoMi',
    'MIT',
    'IBM',
    'Meta',
    'Harvard University',
    'Stanford University',
    'Yale University'
  ];

  // Default metrics if no custom ones are provided
  const defaultMetrics = [
    {
      value: '100,000+',
      label: <Translate id={`${page}.socialProof.users`}>Active Users</Translate>,
      icon: '👥'
    },
    {
      value: '60%',
      label: <Translate id={`${page}.socialProof.productivity`}>Productivity Increase</Translate>,
      icon: '📈'
    },
    {
      value: '80+',
      label: <Translate id={`${page}.socialProof.countries`}>Countries</Translate>,
      icon: '🌎'
    },
    {
      value: '4.8/5',
      label: <Translate id={`${page}.socialProof.rating`}>User Rating</Translate>,
      icon: '⭐'
    },
  ];

  // Use custom data if provided, otherwise use defaults
  const companies = customCompanies || defaultCompanies;
  const metrics = customMetrics || defaultMetrics;

  return (
    <section id="social-proof" className={styles.socialProofSection}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id={titleTranslateId || `${page}.socialProof.title`}>Trusted by Students and Professionals Worldwide</Translate>
        </Heading>

        {showProductHuntBadges && (
          <div className={styles.productHuntContainer}>
            <p className={styles.badgesTitle}>
              <Translate id={`${page}.socialProof.productHunt`}>Featured on Product Hunt</Translate>
            </p>
            <div className={styles.badgesWrapper}>
              <a href="https://www.producthunt.com/posts/funblocks-aiflow?embed=true&utm_source=badge-top-post-badge&utm_medium=badge&utm_souce=badge-funblocks&#0045;aiflow" target="_blank" className={styles.badgeLink}>
                <img src="https://api.producthunt.com/widgets/embed-image/v1/top-post-badge.svg?post_id=486382&theme=dark&period=daily&t=1746785584195"
                  alt="FunBlocks&#0032;AIFlow - An&#0032;AI&#0045;powered&#0032;whiteboard&#0032;and&#0032;mind&#0032;map&#0032;tool | Product Hunt"
                  className={styles.badgeImage} />
              </a>
              <a href="https://www.producthunt.com/posts/funblocks-aiflow?embed=true&utm_source=badge-top-post-topic-badge&utm_medium=badge&utm_souce=badge-funblocks&#0045;aiflow" target="_blank" className={styles.badgeLink}>
                <img src="https://api.producthunt.com/widgets/embed-image/v1/top-post-topic-badge.svg?post_id=486382&theme=dark&period=weekly&topic_id=204&t=1746785584195"
                  alt="FunBlocks&#0032;AIFlow - An&#0032;AI&#0045;powered&#0032;whiteboard&#0032;and&#0032;mind&#0032;map&#0032;tool | Product Hunt"
                  className={styles.badgeImage} />
              </a>
            </div>
          </div>
        )}

        <div className={styles.metricsContainer}>
          {metrics.map((metric, index) => (
            <div key={index} className={styles.metricCard}>
              <div className={styles.metricIcon}>{metric.icon}</div>
              <div className={styles.metricValue}>{metric.value}</div>
              <div className={styles.metricLabel}>{metric.label}</div>
            </div>
          ))}
        </div>

        <div className={styles.companiesContainer}>
          <p className={styles.companiesTitle}>
            <Translate id={companiesTitleTranslateId || `${page}.socialProof.companies`}>Used by innovative teams at</Translate>
          </p>
          <div className={styles.companyGrid}>
            {companies.map((company, index) => (
              <div key={index} className={styles.companyItem}>
                {company}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

export default SocialProofSection;
