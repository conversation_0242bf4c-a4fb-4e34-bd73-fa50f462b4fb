import React, { useState } from 'react';
import clsx from 'clsx';
import Link from '@docusaurus/Link';
import useDocusaurusContext from '@docusaurus/useDocusaurusContext';
import Layout from '@theme/Layout';
import Translate, { translate } from '@docusaurus/Translate';
import Head from '@docusaurus/Head';

import Heading from '@theme/Heading';
import styles from './index.module.css';
import aidocsStyles from './aidocs.module.css';
import Footer from '../components/Footer';
import FAQSection from '../components/FAQSection';
import ImageModal from '../components/ImageModal';
import GoogleAccountAnalytics from '../components/GoogleAccountAnalytics';
import TestimonialsSection from '../components/TestimonialsSection';
import IntroSection from '../components/IntroSection';
import CTASection from '../components/CTASection';
import ComparisonSection from '../components/ComparisonSection';

function AiDocsHeader({ toApp }) {
  return (
    <section className={clsx(styles.hero, styles.pageSection)} style={{ backgroundColor: '#f0f7ff' }}>
      <div className="container" style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Heading as="h1" className={styles.slidesTitle}>
          <Translate id="aidocs.masthead.title">Write Smarter, Think Deeper</Translate>
        </Heading>
        <h2 className={styles.slidesSubtitle}>
          <Translate id="aidocs.masthead.subtitle">Elevate your writing with AI-powered document editor and critical thinking assistance</Translate>
        </h2>
        <div className={styles.heroButtons}>
          <Link
            className={clsx('button', styles.btn)}
            to="#"
            onClick={() => toApp()}
          >
            <Translate id="aidocs.masthead.cta">Try for Free</Translate>
          </Link>
        </div>
      </div>
    </section>
  );
}

function FeaturesSection({ setShowImageSrc }) {
  const features = [
    {
      name: "aidocs.features.item1.name",
      description: "aidocs.features.item1.description",
      image: "/img/portfolio/thumbnails/ai_writer_block_editor.png",
      alt: "Block-based document editor similar to Notion"
    },
    {
      name: "aidocs.features.item2.name",
      description: "aidocs.features.item2.description",
      image: "/img/portfolio/thumbnails/ai_writer_editing_assistant.png",
      alt: "AI writing assistant for content creation"
    },
    {
      name: "aidocs.features.item3.name",
      description: "aidocs.features.item3.description",
      image: "/img/portfolio/thumbnails/ai_writer_infographic_assistant.png",
      alt: "Critical thinking enhancement features"
    },
    {
      name: "aidocs.features.item4.name",
      description: "aidocs.features.item4.description",
      image: "/img/portfolio/thumbnails/ai_writer_critical_thinking_assistant.png",
      alt: "Document organization and linking capabilities"
    },
    {
      name: "aidocs.features.item5.name",
      description: "aidocs.features.item5.description",
      image: "/img/portfolio/thumbnails/ai_writer_workspace.png",
      alt: "Document organization and linking capabilities"
    }
  ];

  return (
    <section id="features" className={styles.slidesFeatureSection}>
      <div className={styles.slidesContainer}>
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="aidocs.features.title">Key Features</Translate>
        </Heading>
        <div>
          {features.map((feature, index) => (
            <div key={index} className={styles.slidesRow}>
              <div className={clsx(styles.slidesCol8, { [styles.order2]: index % 2 === 0 })}>
                <img
                  className={styles.docsFeatureImage}
                  src={feature.image}
                  alt={feature.alt}
                  onClick={() => setShowImageSrc(feature.image.replace('thumbnails', 'fullsize'))}
                />
              </div>
              <div className={clsx(styles.slidesCol4, { [styles.order1]: index % 2 === 0 })}
                style={{
                  justifyContent: 'center',
                  display: 'flex',
                  flexDirection: 'column'
                }}
              >
                <Heading as="h3" className={styles.cardTitle}>
                  <Translate id={feature.name}>{feature.name}</Translate>
                </Heading>
                <p>
                  <Translate id={feature.description}>{feature.description}</Translate>
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

function AIAssistantSection({ setShowImageSrc }) {
  return (
    <section id="ai-assistant" className={styles.featureSection} style={{ backgroundColor: 'aliceblue' }}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="aidocs.ai-assistant.title">
            AI Writing Assistant: Your Thinking Partner
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="aidocs.ai-assistant.subtitle">
            More than just a writing tool - an AI that enhances your critical thinking
          </Translate>
        </p>

        <div className={styles.featureGrid}>
          <div className={styles.featureContent} style={{
            flex: 2,
          }}>
            <Heading as="h3">
              <Translate id="aidocs.ai-assistant.critical-thinking.title">Enhance Critical Thinking</Translate>
            </Heading>
            <p>
              <Translate id="aidocs.ai-assistant.critical-thinking.description">
                FunBlocks AI Docs goes beyond grammar fixes and style improvements to help you think more clearly and write more persuasively.
              </Translate>
            </p>
            <ul className={styles.featureList}>
              <li>
                <Translate id="aidocs.ai-assistant.critical-thinking.point1">
                  Identify cognitive biases in your writing
                </Translate>
              </li>
              <li>
                <Translate id="aidocs.ai-assistant.critical-thinking.point2">
                  Highlight logical fallacies and suggest improvements
                </Translate>
              </li>
              <li>
                <Translate id="aidocs.ai-assistant.critical-thinking.point3">
                  Strengthen arguments by addressing counterpoints
                </Translate>
              </li>
              <li>
                <Translate id="aidocs.ai-assistant.critical-thinking.point4">
                  Analyze the clarity and coherence of your reasoning
                </Translate>
              </li>
            </ul>
          </div>

          <div style={{ cursor: 'pointer', flex: 3 }}>
            <img
              className={styles.docsFeatureImage}
              onClick={() => setShowImageSrc("/img/portfolio/fullsize/ai_writer_critical_thinking_assistant.png")}
              id="aidocs-critical-thinking"
              alt="AI-powered critical thinking enhancement features"
              src="/img/portfolio/thumbnails/ai_writer_critical_thinking_assistant.png"
            />
          </div>
        </div>
      </div>
    </section>
  );
}

function WritingAssistantSection({ setShowImageSrc }) {
  return (
    <section id="writing-assistant" className={styles.featureSection}>
      <div className="container">
        <div className={styles.featureGrid}>
          <div style={{ cursor: 'pointer', flex: 3 }}>
            <img
              className={styles.docsFeatureImage}
              onClick={() => setShowImageSrc("/img/portfolio/fullsize/ai_writer_editing_assistant.png")}
              id="aidocs-writing-assistant"
              alt="AI-powered writing assistance features"
              src="/img/portfolio/thumbnails/ai_writer_editing_assistant.png"
            />
          </div>

          <div className={styles.featureContent} style={{
            flex: 2
          }}>
            <Heading as="h3">
              <Translate id="aidocs.writing-assistant.title">Smart Writing Tools</Translate>
            </Heading>
            <p>
              <Translate id="aidocs.writing-assistant.description">
                Create high-quality content efficiently with powerful AI-powered writing tools.
              </Translate>
            </p>
            <ul className={styles.featureList}>
              <li>
                <Translate id="aidocs.writing-assistant.point1">
                  Generate complete documents from a simple topic or outline
                </Translate>
              </li>
              <li>
                <Translate id="aidocs.writing-assistant.point2">
                  Rewrite and refine selected text with targeted instructions
                </Translate>
              </li>
              <li>
                <Translate id="aidocs.writing-assistant.point3">
                  Intelligent continuation suggestions based on your writing style
                </Translate>
              </li>
              <li>
                <Translate id="aidocs.writing-assistant.point4">
                  Grammar and style correction with detailed explanations
                </Translate>
              </li>
              <li>
                <Translate id="aidocs.writing-assistant.point5">
                  Grammar and style correction with detailed explanations
                </Translate>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}

function EcosystemIntegrationSection({ setShowImageSrc }) {
  return (
    <section id="ecosystem-integration" className={styles.featureSection} style={{ backgroundColor: 'cornsilk' }}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="aidocs.ecosystem.title">
            Seamless FunBlocks Ecosystem Integration
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="aidocs.ecosystem.subtitle">
            Part of a powerful AI workspace that enhances your entire workflow
          </Translate>
        </p>

        <div className={styles.featureGrid}>
          <div className={styles.featureContent} style={{
            flex: 2,
          }}>
            <Heading as="h3">
              <Translate id="aidocs.ecosystem.workflow.title">Complete Workflow Integration</Translate>
            </Heading>
            <ul className={styles.featureList}>
              <li>
                <Translate id="aidocs.ecosystem.workflow.point1">
                  Convert AIFlow mind maps directly into structured documents with one click
                </Translate>
              </li>
              <li>
                <Translate id="aidocs.ecosystem.workflow.point2">
                  Transform any document into professional slides for presentations
                </Translate>
              </li>
              <li>
                <Translate id="aidocs.ecosystem.workflow.point3">
                  Turn document content into visual mind maps for deeper exploration
                </Translate>
              </li>
              <li>
                <Translate id="aidocs.ecosystem.workflow.point4">
                  Create a comprehensive workspace by linking documents, mind maps and presentations
                </Translate>
              </li>
              <li>
                <Translate id="aidocs.ecosystem.workflow.point5">
                  Create a comprehensive workspace by linking documents, mind maps and presentations
                </Translate>
              </li>
            </ul>
          </div>

          <div style={{ cursor: 'pointer', flex: 3 }}>
            <img
              className={styles.featureImage}
              onClick={() => setShowImageSrc("/img/portfolio/fullsize/ai_workspace.png")}
              id="aidocs-ecosystem"
              alt="FunBlocks ecosystem integration features"
              src="/img/portfolio/thumbnails/ai_workspace.png"
            />
          </div>
        </div>
      </div>
    </section>
  );
}

function ThinkingEnhancementSection({ setShowImageSrc }) {
  return (
    <section id="thinking-enhancement" className={styles.featureSection} style={{ backgroundColor: '#f5f8ff' }}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="aidocs.thinking-enhancement.title">
            Writing as a Thinking Tool
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="aidocs.thinking-enhancement.description">
            Writing is not just for output—it's a powerful method for learning and enhancing cognitive abilities. FunBlocks AI ecosystem provides a complete solution for your thinking journey.
          </Translate>
        </p>

        <div className={aidocsStyles.learningCycle}>
          <div className={aidocsStyles.cycleIntro}>
            <Heading as="h3" className={aidocsStyles.cycleTitle}>
              <Translate id="aidocs.thinking-enhancement.cycle.title">
                The Complete Thinking Cycle
              </Translate>
            </Heading>
            <p className={aidocsStyles.cycleDescription}>
              <Translate id="aidocs.thinking-enhancement.cycle.description">
                FunBlocks AI integrates all stages of the thinking process—from initial ideas to structured knowledge—creating a powerful ecosystem for learning and intellectual growth.
              </Translate>
            </p>
          </div>
          <div className={aidocsStyles.cycleSteps}>
            <div className={aidocsStyles.cycleStep}>
              <div className={aidocsStyles.stepNumber}>1</div>
              <div className={aidocsStyles.stepContent}>
                <Heading as="h4">
                  <Translate id="aidocs.thinking-enhancement.cycle.step1.title">
                    Explore & Discover
                  </Translate>
                </Heading>
                <p>
                  <Translate id="aidocs.thinking-enhancement.cycle.step1.description">
                    Begin with AI-powered brainstorming to explore topics and generate initial ideas
                  </Translate>
                </p>
              </div>
            </div>
            <div className={aidocsStyles.cycleStep}>
              <div className={aidocsStyles.stepNumber}>2</div>
              <div className={aidocsStyles.stepContent}>
                <Heading as="h4">
                  <Translate id="aidocs.thinking-enhancement.cycle.step2.title">
                    Visualize & Connect
                  </Translate>
                </Heading>
                <p>
                  <Translate id="aidocs.thinking-enhancement.cycle.step2.description">
                    Organize thoughts with mind maps to see relationships and identify patterns
                  </Translate>
                </p>
              </div>
            </div>
            <div className={aidocsStyles.cycleStep}>
              <div className={aidocsStyles.stepNumber}>3</div>
              <div className={aidocsStyles.stepContent}>
                <Heading as="h4">
                  <Translate id="aidocs.thinking-enhancement.cycle.step3.title">
                    Create & Develop
                  </Translate>
                </Heading>
                <p>
                  <Translate id="aidocs.thinking-enhancement.cycle.step3.description">
                    Transform ideas into structured documents with creative thinking tools
                  </Translate>
                </p>
              </div>
            </div>
            <div className={aidocsStyles.cycleStep}>
              <div className={aidocsStyles.stepNumber}>4</div>
              <div className={aidocsStyles.stepContent}>
                <Heading as="h4">
                  <Translate id="aidocs.thinking-enhancement.cycle.step4.title">
                    Analyze & Refine
                  </Translate>
                </Heading>
                <p>
                  <Translate id="aidocs.thinking-enhancement.cycle.step4.description">
                    Apply critical thinking to strengthen arguments and eliminate biases
                  </Translate>
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className={aidocsStyles.thinkingTools}>
          <Heading as="h3" className={aidocsStyles.toolsTitle}>
            <Translate id="aidocs.thinking-enhancement.tools.title">
              AI-Powered Thinking Tools
            </Translate>
          </Heading>

          <div className={aidocsStyles.toolsGrid}>
            <div className={aidocsStyles.toolCard}>
              <div className={aidocsStyles.toolIcon}>⚡</div>
              <Heading as="h4">
                <Translate id="aidocs.brainstorming.title">AI-Powered Brainstorming</Translate>
              </Heading>
              <p>
                <Translate id="aidocs.thinking-enhancement.tools.brainstorming">
                  Generate ideas using AI-guided frameworks like SCAMPER, Six Thinking Hats, and First Principles thinking. Expand seed concepts into comprehensive outlines.
                </Translate>
              </p>
            </div>

            <div className={aidocsStyles.toolCard}>
              <div className={aidocsStyles.toolIcon}>🗺️</div>
              <Heading as="h4">
                <Translate id="aidocs.mindmap.title">Mind Map Integration</Translate>
              </Heading>
              <p>
                <Translate id="aidocs.thinking-enhancement.tools.mindmap">
                  Visualize complex relationships between ideas with one-click conversion between documents and mind maps. Enhance comprehension and retention through visual thinking.
                </Translate>
              </p>
            </div>

            <div className={aidocsStyles.toolCard}>
              <div className={aidocsStyles.toolIcon}>💡</div>
              <Heading as="h4">
                <Translate id="aidocs.creative-thinking.title">Creative Thinking Tools</Translate>
              </Heading>
              <p>
                <Translate id="aidocs.thinking-enhancement.tools.creative">
                  View content from multiple perspectives, generate analogies and metaphors, and challenge limiting assumptions to discover innovative solutions and insights.
                </Translate>
              </p>
            </div>

            <div className={aidocsStyles.toolCard}>
              <div className={aidocsStyles.toolIcon}>⚖️</div>
              <Heading as="h4">
                <Translate id="aidocs.thinking-enhancement.tools.critical.title">Critical Thinking Assistant</Translate>
              </Heading>
              <p>
                <Translate id="aidocs.thinking-enhancement.tools.critical">
                  Identify cognitive biases, highlight logical fallacies, strengthen arguments by addressing counterpoints, and analyze the clarity of your reasoning.
                </Translate>
              </p>
            </div>
          </div>
        </div>

        <div className={aidocsStyles.learningQuote}>
          <blockquote>
            <p>
              <Translate id="aidocs.thinking-enhancement.quote">
                "Writing is not just communicating ideas; it's a powerful tool for developing them. FunBlocks AI transforms writing from mere documentation into an active learning process."
              </Translate>
            </p>
          </blockquote>
        </div>

        {/* <div className={aidocsStyles.thinkingVisual}>
          <div style={{ cursor: 'pointer' }}>
            <img
              className={styles.featureImage}
              src="/img/portfolio/thumbnails/thinking_enhancement_combined.png"
              alt="Complete thinking cycle with FunBlocks AI ecosystem"
              onClick={() => setShowImageSrc("/img/portfolio/fullsize/thinking_enhancement_combined.png")}
            />
          </div>
        </div> */}
      </div>
    </section>
  );
}

function UseCasesSection() {
  const useCases = [
    {
      icon: '📚',
      titleId: 'aidocs.use-cases.case1.title',
      descriptionId: 'aidocs.use-cases.case1.description',
    },
    {
      icon: '📝',
      titleId: 'aidocs.use-cases.case2.title',
      descriptionId: 'aidocs.use-cases.case2.description',
    },
    {
      icon: '🎓',
      titleId: 'aidocs.use-cases.case3.title',
      descriptionId: 'aidocs.use-cases.case3.description',
    },
    {
      icon: '💼',
      titleId: 'aidocs.use-cases.case4.title',
      descriptionId: 'aidocs.use-cases.case4.description',
    },
    {
      icon: '📊',
      titleId: 'aidocs.use-cases.case5.title',
      descriptionId: 'aidocs.use-cases.case5.description',
    },
    {
      icon: '💡',
      titleId: 'aidocs.use-cases.case6.title',
      descriptionId: 'aidocs.use-cases.case6.description',
    },
  ];

  return (
    <section id="use-cases" className={styles.useCases}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="aidocs.use-cases.title">Use Cases</Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="aidocs.use-cases.description">FunBlocks AI Docs adapts to diverse content creation scenarios, enhancing both productivity and quality</Translate>
        </p>

        <div className={styles.twoColumnGrid}>
          {useCases.map((useCase, index) => (
            <div key={index} className={styles.useCaseCard}>
              <div className={styles.cardTitle}>
                <div className={styles.useCaseIcon}>{useCase.icon}</div>
                <span>
                  <Translate id={useCase.titleId}>Case Title</Translate>
                </span>
              </div>
              <p>
                <Translate id={useCase.descriptionId}>Case description</Translate>
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

// 新增的统计数据部分
function StatsSection() {
  return (
    <section id="stats" className={aidocsStyles.statsSection}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="aidocs.stats.title">Trusted by Students and Professionals Worldwide</Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="aidocs.stats.description">
            Join thousands of professionals who use FunBlocks AI Docs to enhance their writing and thinking
          </Translate>
        </p>

        <div className={aidocsStyles.statsGrid}>
          <div className={aidocsStyles.statCard}>
            <div className={aidocsStyles.statNumber}>100,000+</div>
            <div className={aidocsStyles.statLabel}>
              <Translate id="aidocs.stats.users">Active Users</Translate>
            </div>
          </div>
          <div className={aidocsStyles.statCard}>
            <div className={aidocsStyles.statNumber}>1.2M+</div>
            <div className={aidocsStyles.statLabel}>
              <Translate id="aidocs.stats.documents">Documents Created</Translate>
            </div>
          </div>
          <div className={aidocsStyles.statCard}>
            <div className={aidocsStyles.statNumber}>40%</div>
            <div className={aidocsStyles.statLabel}>
              <Translate id="aidocs.stats.timeSaved">Average Time Saved</Translate>
            </div>
          </div>
          <div className={aidocsStyles.statCard}>
            <div className={aidocsStyles.statNumber}>4.8/5</div>
            <div className={aidocsStyles.statLabel}>
              <Translate id="aidocs.stats.satisfaction">User Satisfaction</Translate>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// 结构化数据组件
function StructuredData() {
  // 获取当前URL
  const getUrl = () => {
    if (typeof window !== 'undefined') {
      return window.location.href;
    }
    return 'https://www.funblocks.net/aidocs';
  };

  // 构建结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "FunBlocks AI Docs",
    "applicationCategory": "ProductivityApplication",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "description": "AI-powered document editor with critical thinking, brainstorming, and mind mapping capabilities to enhance your writing and learning process.",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "1250",
      "bestRating": "5",
      "worstRating": "1"
    },
    "featureList": [
      "AI-powered writing assistant",
      "Critical thinking enhancement",
      "Brainstorming tools",
      "Mind map integration",
      "Creative thinking tools",
      "Block-based editor"
    ],
    "screenshot": "https://www.funblocks.net/img/portfolio/fullsize/ai_writer_editor.png",
    "url": getUrl(),
    "keywords": "AI document editor, critical thinking, brainstorming, mind mapping, creative thinking, learning tool",
    "applicationSubCategory": "Document Editor",
    "author": {
      "@type": "Organization",
      "name": "FunBlocks",
      "url": "https://www.funblocks.net"
    }
  };

  return (
    <Head>
      <script type="application/ld+json">
        {JSON.stringify(structuredData)}
      </script>
    </Head>
  );
}

// FAQ结构化数据组件
function FAQStructuredData() {
  // FAQ结构化数据
  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "What is FunBlocks AI Docs and how does it improve document writing and editing?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "FunBlocks AI Docs is a comprehensive AI-powered document editor that combines Notion-like block editing with advanced artificial intelligence to enhance your writing process, critical thinking abilities, and overall productivity. The platform features a flexible block-based editor similar to Notion, powerful AI writing assistance, critical thinking enhancement tools that identify biases and logical fallacies, and seamless integration with other FunBlocks products like AIFlow mind maps and AI Slides."
        }
      },
      {
        "@type": "Question",
        "name": "How does the AI Writing Assistant in FunBlocks AI Docs help improve my writing quality and efficiency?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "The AI Writing Assistant in FunBlocks AI Docs functions as your personal thinking partner to elevate both writing quality and efficiency. It provides four key capabilities: 1) Generate complete documents from scratch based on simple topics or outlines, 2) Intelligently rewrite and refine selected text based on specific instructions, 3) Offer smart continuation suggestions that match your personal writing style, and 4) Provide comprehensive grammar and style corrections with detailed explanations."
        }
      },
      {
        "@type": "Question",
        "name": "What makes the Critical Thinking Enhancement feature in FunBlocks AI Docs unique compared to other writing tools?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "FunBlocks AI Docs' Critical Thinking Enhancement feature goes far beyond what traditional writing tools offer by focusing on the quality of your reasoning, not just your writing mechanics. This unique feature helps you think more clearly and write more persuasively through four advanced capabilities: 1) Identifying specific cognitive biases in your writing that might weaken your arguments, 2) Highlighting logical fallacies and providing clear suggestions for improvement, 3) Strengthening your arguments by identifying and addressing potential counterarguments, and 4) Analyzing the clarity and coherence of your reasoning."
        }
      },
      {
        "@type": "Question",
        "name": "How does FunBlocks AI Docs integrate with AIFlow mind maps and AI Slides to create a complete workflow system?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "FunBlocks AI Docs deeply integrates with AIFlow mind maps and AI Slides through a comprehensive ecosystem approach, creating a seamless all-in-one workflow. This integration enables four powerful workflow capabilities: 1) One-click conversion of AIFlow mind maps directly into documents with appropriate hierarchy and organization, 2) Automatic transformation of any document into professional presentation slides suitable for meetings or conferences, 3) Quick conversion of document content into visual mind maps for concept exploration and relationship mapping, and 4) Creation of a unified knowledge workspace through bidirectional linking between documents, mind maps, and presentations."
        }
      },
      {
        "@type": "Question",
        "name": "How can FunBlocks AI Docs help with brainstorming and creative thinking?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "FunBlocks AI Docs transforms writing from mere output into a powerful learning and thinking tool. It provides integrated brainstorming tools that help generate, organize, and develop ideas using AI-guided frameworks like SCAMPER and Six Thinking Hats. The creative thinking tools allow you to view content from different perspectives, generate analogies and metaphors, and challenge limiting assumptions. With seamless mind map integration, you can visualize complex relationships between ideas and enhance comprehension through visual thinking."
        }
      }
    ]
  };

  return (
    <Head>
      <script type="application/ld+json">
        {JSON.stringify(faqStructuredData)}
      </script>
    </Head>
  );
}

// 面包屑导航结构化数据组件
function BreadcrumbStructuredData() {
  // 获取当前URL
  const getUrl = () => {
    if (typeof window !== 'undefined') {
      return window.location.href;
    }
    return 'https://www.funblocks.net/aidocs';
  };

  // 面包屑导航结构化数据
  const breadcrumbData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": "https://www.funblocks.net"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Products",
        "item": "https://www.funblocks.net/products"
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": "FunBlocks AI Docs",
        "item": getUrl()
      }
    ]
  };

  return (
    <Head>
      <script type="application/ld+json">
        {JSON.stringify(breadcrumbData)}
      </script>
    </Head>
  );
}

export default function AIDocs() {
  const [showImageSrc, setShowImageSrc] = useState(null);

  function getDomain() {
    if (!window.location.hostname.includes('funblocks')) {
      return 'funblocks.net';
    }
    return window.location.hostname.replace('www.', '');
  }

  function openUrl(url) {
    let newTab = window.open();
    newTab.location.href = url;
  }

  function toApp() {
    let url = `https://app.${getDomain()}/#/login?source=aidocs`;
    openUrl(url);
  }

  const testimonials_avatars = ["👩‍🎓", "👨‍💼", "👩‍💻", "👨‍🎓", "👩‍🏫", "👨‍🏫"];

  return (
    <Layout
      title={translate({
        id: 'aidocs.head.title',
        message: 'FunBlocks AI Docs: AI-powered Block Editor & Critical Thinking Assistant | Brainstorming & Mind Mapping'
      })}
      description={translate({
        id: 'aidocs.head.description',
        message: 'FunBlocks AI Docs combines Notion-like editing with AI assistance to enhance your writing, critical thinking, and brainstorming capabilities. Create better content faster with integrated mind mapping and creative thinking tools.'
      })}
    >
      <StructuredData />
      <FAQStructuredData />
      <BreadcrumbStructuredData />
      <AiDocsHeader toApp={toApp} />
      <main>
        <IntroSection
          page="aidocs"
          feature={'intro'}
          pointNos={[1, 2, 3, 4, 5]}
          style={{backgroundColor: 'lightcyan'}}
          imageElement={<div style={{ flex: 4, cursor: 'pointer' }}>
            <img
              className={styles.featureImage}
              src="/img/portfolio/thumbnails/ai_writer_editor.png"
              alt="FunBlocks AI Docs: Notion-style block editor with AI assistant"
              onClick={() => setShowImageSrc("/img/portfolio/fullsize/ai_writer_editor.png")}
            />
          </div>}
        />
        <FeaturesSection setShowImageSrc={setShowImageSrc} />

        <AIAssistantSection setShowImageSrc={setShowImageSrc} />
        <WritingAssistantSection setShowImageSrc={setShowImageSrc} />

        {/* 整合的思维增强部分 */}
        <ThinkingEnhancementSection setShowImageSrc={setShowImageSrc} />

        <EcosystemIntegrationSection setShowImageSrc={setShowImageSrc} />

        <IntroSection
          page="aidocs"
          feature={'organization'}
          pointNos={[1, 2, 3, 4]}
          style={{backgroundColor: 'lavender'}}
          imageElement={<div style={{ flex: 4, cursor: 'pointer' }}>
            <img
              className={styles.docsFeatureImage}
              src="/img/portfolio/thumbnails/ai_writer_workspace.png"
              alt="FunBlocks AI Docs: Notion-style block editor with AI assistant"
              onClick={() => setShowImageSrc("/img/portfolio/fullsize/ai_writer_workspace.png")}
            />
          </div>}
        />

        {/* 新增的比较部分 */}
        <ComparisonSection
          page="aidocs"
          competitors={{
            funblocks: {
              label: <Translate id="aidocs.comparison.funblocksHeader">FunBlocks AI Docs</Translate>,
              isHighlighted: true
            },
            googleDocs: {
              label: <Translate id="aidocs.comparison.googleDocsHeader">Google Docs</Translate>,
              isHighlighted: false
            },
            notion: {
              label: <Translate id="aidocs.comparison.notionHeader">Notion</Translate>,
              isHighlighted: false
            },
            grammarly: {
              label: <Translate id="aidocs.comparison.grammarly">Grammarly</Translate>,
              isHighlighted: false
            }
          }}
          customData={[
            {
              feature: <Translate id="aidocs.comparison.feature1">Block-Based Editor</Translate>,
              funblocks: true,
              googleDocs: 'Limited',
              notion: true,
              grammarly: false
            },
            {
              feature: <Translate id="aidocs.comparison.feature2">AI Writing Assistant</Translate>,
              funblocks: true,
              googleDocs: 'Limited',
              notion: true,
              grammarly: true
            },
            {
              feature: <Translate id="aidocs.comparison.feature3">Critical Thinking Enhancement</Translate>,
              funblocks: true,
              googleDocs: false,
              notion: false,
              grammarly: false
            },
            {
              feature: <Translate id="aidocs.comparison.feature4">Brainstorming Tools</Translate>,
              funblocks: true,
              googleDocs: false,
              notion: false,
              grammarly: false
            },
            {
              feature: <Translate id="aidocs.comparison.feature5">Mind Map Integration</Translate>,
              funblocks: true,
              googleDocs: false,
              notion: false,
              grammarly: false
            },
            {
              feature: <Translate id="aidocs.comparison.feature6">Infographics Generation</Translate>,
              funblocks: true,
              googleDocs: false,
              notion: false,
              grammarly: false
            },
            {
              feature: <Translate id="aidocs.comparison.feature7">Creative Thinking Tools</Translate>,
              funblocks: true,
              googleDocs: false,
              notion: false,
              grammarly: false
            },
            {
              feature: <Translate id="aidocs.comparison.feature8">Document Organization</Translate>,
              funblocks: true,
              googleDocs: 'Basic',
              notion: true,
              grammarly: false
            }
          ]}
        />

        <UseCasesSection />
        <StatsSection />
        <TestimonialsSection avatars={testimonials_avatars} page={'aidocs'} />
        <CTASection toApp={toApp} page={'aidocs'} />
        <FAQSection
          page={'aidocs'}
          faqIds={[
            'q1', 'q2', 'q3', 'q4', 'q5', 'q6', 'q7', 'q8',
            'q9', 'q10', 'q11', 'q12'
          ]}
        />
      </main>
      <Footer />

      {showImageSrc && <ImageModal imageSrc={showImageSrc} setImageSrc={setShowImageSrc} />}
      <GoogleAccountAnalytics page={'aidocs'} />
    </Layout>
  );
}